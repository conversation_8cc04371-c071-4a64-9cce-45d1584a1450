# Use Python 3.11 slim image for smaller size
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies if needed
RUN apt-get update && apt-get install -y \
    --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY backup_exporter.py .

# Create directory for metrics output
RUN mkdir -p /tmp

# Create non-root user for security
RUN useradd --create-home --shell /bin/bash exporter
USER exporter

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Default command
CMD ["python", "backup_exporter.py", "--backup-path", "/backup", "--output-file", "/tmp/backup_metrics.prom", "--interval", "5"]

# Health check
HEALTHCHECK --interval=60s --timeout=10s --start-period=30s --retries=3 \
    CMD test -f /tmp/backup_metrics.prom || exit 1

# Labels
LABEL maintainer="backup-exporter"
LABEL description="Backup folder metrics exporter for Prometheus"
LABEL version="1.0"
