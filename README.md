# Backup Folder Exporter (Docker)

Exporter đơn giản để theo dõi folder chứa backup và export metrics cho Prometheus qua **Node Exporter Textfile Collector**.  

## Metrics

Exporter sinh ra file `.prom` với các metric:

- `folder_exists{path="/backup"}`
- `folder_size_bytes{path="/backup"}`
- `folder_file_count_total{path="/backup"}`
- `folder_latest_file_timestamp{path="/backup"}`
- `folder_latest_file_size_bytes{path="/backup"}`

Ví dụ:
```text
folder_exists{path="/backup"} 1
folder_size_bytes{path="/backup"} 53687091200
folder_file_count_total{path="/backup"} 42
folder_latest_file_timestamp{path="/backup"} 1725345661
folder_latest_file_size_bytes{path="/backup"} 1073741824
```

## Cách chạy bằng Docker
```bash
docker run -d \
  --name backup-exporter \
  -v /path/to/backup/folder:/backup:ro \
  -v /var/lib/node_exporter/textfile_collector:/exporter \
  backup-folder-exporter
```