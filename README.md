# Backup Folder Exporter (Docker)

Exporter đơn giản để theo dõi folder chứa backup và export metrics cho Prometheus qua API.

## Metrics

Exporter sẽ trả về các metric:

- `folder_exists{path="/backup"}` - Folder có tồn tại hay không (1/0)
- `folder_size_bytes{path="/backup"}` - Tổng kích thước folder (bytes)
- `folder_file_count_total{path="/backup"}` - Tổng số file trong folder
- `folder_latest_file_timestamp{path="/backup"}` - Timestamp của file mới nhất
- `folder_latest_file_size_bytes{path="/backup"}` - Kích thước file mới nhất (bytes)

Ví dụ:
```text
folder_exists{path="/backup"} 1
folder_size_bytes{path="/backup"} 53687091200
folder_file_count_total{path="/backup"} 42
folder_latest_file_timestamp{path="/backup"} 1725345661
folder_latest_file_size_bytes{path="/backup"} 1073741824
```

## Cài đặt và Sử dụng

### 1. Sử dụng Python Script trực tiếp

#### Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

#### Chạy một lần:
```bash
python backup_exporter.py --backup-path /path/to/backup --output-file /tmp/backup_metrics.prom --once
```

#### Chạy liên tục (mặc định 5 phút/lần):
```bash
python backup_exporter.py --backup-path /path/to/backup --output-file /tmp/backup_metrics.prom --interval 5
```

#### Các tham số:
- `--backup-path`: Đường dẫn đến folder backup cần theo dõi (bắt buộc)
- `--output-file`: File output cho metrics (mặc định: `/tmp/backup_metrics.prom`)
- `--interval`: Khoảng thời gian giữa các lần export (phút, mặc định: 5)
- `--once`: Chạy một lần rồi thoát

### 2. Sử dụng Docker

#### Build image:
```bash
docker build -t backup-exporter .
```

#### Chạy container:
```bash
docker run -d \
  --name backup-exporter \
  -v /path/to/your/backup:/backup:ro \
  -v ./metrics:/tmp:rw \
  backup-exporter
```

### 3. Sử dụng Docker Compose (Khuyến nghị)

#### Cấu hình:
1. Sửa file `docker-compose.yml`, thay đổi đường dẫn backup:
```yaml
volumes:
  - /path/to/your/backup:/backup:ro  # Thay đổi đường dẫn này
```

#### Chạy:
```bash
# Khởi động
docker-compose up -d

# Xem logs
docker-compose logs -f backup-exporter

# Dừng
docker-compose down
```

### 4. Tích hợp với Prometheus

#### Với Node Exporter Textfile Collector:
1. Cấu hình Node Exporter để đọc từ thư mục metrics:
```bash
node_exporter --collector.textfile.directory=/path/to/metrics
```

2. Hoặc sử dụng Docker Compose đã bao gồm Node Exporter:
```bash
docker-compose up -d
```
Node Exporter sẽ chạy trên port 9100.

#### Prometheus Configuration:
```yaml
scrape_configs:
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
```

## Cấu trúc Project

```
backup-exporter/
├── backup_exporter.py      # Python script chính
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker image definition
├── docker-compose.yml     # Docker Compose configuration
├── README.md              # Tài liệu này
└── metrics/               # Thư mục output metrics (tạo tự động)
```

## Monitoring và Troubleshooting

### Kiểm tra metrics:
```bash
# Xem file metrics
cat ./metrics/backup_metrics.prom

# Kiểm tra container logs
docker-compose logs backup-exporter

# Kiểm tra health status
docker-compose ps
```

### Health Check:
Container có health check tự động kiểm tra file metrics được tạo ra.

## Yêu cầu hệ thống

- Python 3.11+
- Docker & Docker Compose (nếu sử dụng container)
- Quyền đọc folder backup
- Quyền ghi thư mục output metrics