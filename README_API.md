# Backup Folder Exporter (HTTP API)

Exporter đơn giản để theo dõi folder chứa backup và export metrics cho Prometheus qua **HTTP API**.  

## Metrics

Exporter cung cấp các metric qua HTTP endpoints:

- `folder_exists{path="/backup"}` - Folder có tồn tại hay không (1/0)
- `folder_size_bytes{path="/backup"}` - Tổng kích thước folder (bytes)
- `folder_file_count_total{path="/backup"}` - Tổng số file trong folder
- `folder_latest_file_timestamp{path="/backup"}` - Timestamp của file mới nhất
- `folder_latest_file_size_bytes{path="/backup"}` - <PERSON><PERSON><PERSON> thước file mới nhất (bytes)

## HTTP Endpoints

- `GET /metrics` - Prometheus format metrics
- `GET /metrics/json` - JSON format metrics
- `GET /health` - Health check
- `GET /` - API documentation

### Ví dụ Prometheus format (`/metrics`):
```text
folder_exists{path="/backup"} 1
folder_size_bytes{path="/backup"} 53687091200
folder_file_count_total{path="/backup"} 42
folder_latest_file_timestamp{path="/backup"} **********
folder_latest_file_size_bytes{path="/backup"} **********
```

### Ví dụ JSON format (`/metrics/json`):
```json
{
  "metrics": {
    "folder_exists": 1,
    "folder_size_bytes": 53687091200,
    "folder_file_count_total": 42,
    "folder_latest_file_timestamp": **********,
    "folder_latest_file_size_bytes": **********
  },
  "last_update": "2024-09-03T10:30:00.123456",
  "backup_path": "/backup"
}
```

## Cài đặt và Sử dụng

### 1. Sử dụng Python Script trực tiếp

#### Cài đặt dependencies:
```bash
pip install -r requirements.txt
```

#### Chạy HTTP API server:
```bash
python backup_exporter_api.py --backup-path /path/to/backup --port 8080 --host 0.0.0.0
```

#### Các tham số:
- `--backup-path`: Đường dẫn đến folder backup cần theo dõi (bắt buộc)
- `--port`: Port cho HTTP server (mặc định: 8080)
- `--host`: Host để bind (mặc định: 0.0.0.0)
- `--update-interval`: Khoảng thời gian cập nhật metrics (giây, mặc định: 60)

#### Test API:
```bash
# Xem metrics Prometheus format
curl http://localhost:8080/metrics

# Xem metrics JSON format
curl http://localhost:8080/metrics/json

# Health check
curl http://localhost:8080/health
```

### 2. Sử dụng Docker

#### Build image:
```bash
docker build -t backup-exporter .
```

#### Chạy container:
```bash
docker run -d \
  --name backup-exporter \
  -p 8080:8080 \
  -v /path/to/your/backup:/backup:ro \
  backup-exporter
```

### 3. Sử dụng Docker Compose (Khuyến nghị)

#### Cấu hình:
1. Sửa file `docker-compose.yml`, thay đổi đường dẫn backup:
```yaml
volumes:
  - /path/to/your/backup:/backup:ro  # Thay đổi đường dẫn này
```

#### Chạy:
```bash
# Khởi động (bao gồm cả Prometheus)
docker-compose up -d

# Xem logs
docker-compose logs -f backup-exporter

# Dừng
docker-compose down
```

#### Truy cập services:
- **Backup Exporter API**: http://localhost:8080
- **Prometheus**: http://localhost:9090

### 4. Tích hợp với Prometheus

#### Prometheus Configuration:
```yaml
scrape_configs:
  - job_name: 'backup-exporter'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/metrics'
    scrape_interval: 30s
```

#### Hoặc sử dụng Docker Compose:
File `prometheus.yml` đã được cấu hình sẵn để scrape từ backup-exporter.

## Cấu trúc Project

```
backup-exporter/
├── backup_exporter_api.py  # Python HTTP API server
├── backup_exporter.py      # Python script cũ (file-based)
├── requirements.txt        # Python dependencies
├── Dockerfile             # Docker image definition
├── docker-compose.yml     # Docker Compose configuration
├── prometheus.yml         # Prometheus configuration
├── README.md              # Tài liệu này
└── example_usage.sh       # Script example
```

## Monitoring và Troubleshooting

### Kiểm tra API:
```bash
# Test tất cả endpoints
curl http://localhost:8080/
curl http://localhost:8080/health
curl http://localhost:8080/metrics
curl http://localhost:8080/metrics/json

# Kiểm tra container logs
docker-compose logs backup-exporter

# Kiểm tra health status
docker-compose ps
```

### Health Check:
- Container có health check tự động gọi `/health` endpoint
- API có background thread cập nhật metrics định kỳ

### Prometheus Queries:
```promql
# Kiểm tra folder có tồn tại
folder_exists{path="/backup"}

# Kích thước folder (GB)
folder_size_bytes{path="/backup"} / 1024 / 1024 / 1024

# Số lượng file
folder_file_count_total{path="/backup"}

# Thời gian file mới nhất (hours ago)
(time() - folder_latest_file_timestamp{path="/backup"}) / 3600
```

## Yêu cầu hệ thống

- Python 3.11+
- Flask 2.3+
- Docker & Docker Compose (nếu sử dụng container)
- Quyền đọc folder backup
- Port 8080 available (hoặc port tùy chỉnh)

## Performance Notes

- Metrics được cache và cập nhật theo interval (mặc định 60s)
- API response nhanh vì sử dụng cached data
- Background thread scan folder định kỳ
- Phù hợp cho folder backup có kích thước lớn
