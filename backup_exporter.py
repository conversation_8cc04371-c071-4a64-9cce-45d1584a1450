#!/usr/bin/env python3
"""
Backup Folder Exporter for Prometheus
Monitors backup folders and exports metrics in Prometheus format
"""

import os
import time
import argparse
import logging
from pathlib import Path
from typing import Dict, Any, Optional
import schedule
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BackupExporter:
    """Backup folder metrics exporter for Prometheus"""
    
    def __init__(self, backup_path: str, output_file: str = "/tmp/backup_metrics.prom"):
        """
        Initialize the backup exporter
        
        Args:
            backup_path: Path to the backup folder to monitor
            output_file: Path to output .prom file for Node Exporter
        """
        self.backup_path = Path(backup_path)
        self.output_file = Path(output_file)
        self.metrics = {}
        
        # Ensure output directory exists
        self.output_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized BackupExporter for path: {self.backup_path}")
        logger.info(f"Output file: {self.output_file}")
    
    def collect_metrics(self) -> Dict[str, Any]:
        """
        Collect metrics from the backup folder
        
        Returns:
            Dictionary containing all metrics
        """
        metrics = {
            'folder_exists': 0,
            'folder_size_bytes': 0,
            'folder_file_count_total': 0,
            'folder_latest_file_timestamp': 0,
            'folder_latest_file_size_bytes': 0
        }
        
        try:
            # Check if folder exists
            if not self.backup_path.exists():
                logger.warning(f"Backup path does not exist: {self.backup_path}")
                return metrics
            
            if not self.backup_path.is_dir():
                logger.warning(f"Backup path is not a directory: {self.backup_path}")
                return metrics
            
            metrics['folder_exists'] = 1
            
            # Calculate folder size and file count
            total_size = 0
            file_count = 0
            latest_file_time = 0
            latest_file_size = 0
            
            for file_path in self.backup_path.rglob('*'):
                if file_path.is_file():
                    try:
                        file_stat = file_path.stat()
                        file_size = file_stat.st_size
                        file_mtime = file_stat.st_mtime
                        
                        total_size += file_size
                        file_count += 1
                        
                        # Track latest file
                        if file_mtime > latest_file_time:
                            latest_file_time = file_mtime
                            latest_file_size = file_size
                            
                    except (OSError, IOError) as e:
                        logger.warning(f"Error accessing file {file_path}: {e}")
                        continue
            
            metrics['folder_size_bytes'] = total_size
            metrics['folder_file_count_total'] = file_count
            metrics['folder_latest_file_timestamp'] = int(latest_file_time)
            metrics['folder_latest_file_size_bytes'] = latest_file_size
            
            logger.info(f"Collected metrics: {file_count} files, {total_size} bytes total")
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
        
        return metrics
    
    def write_prometheus_metrics(self, metrics: Dict[str, Any]) -> None:
        """
        Write metrics to Prometheus format file
        
        Args:
            metrics: Dictionary containing metrics to write
        """
        try:
            with open(self.output_file, 'w') as f:
                # Write header comment
                f.write(f"# Backup folder metrics generated at {datetime.now().isoformat()}\n")
                f.write(f"# Monitoring path: {self.backup_path}\n\n")
                
                # Write metrics
                backup_path_str = str(self.backup_path)
                f.write(f'folder_exists{{path="{backup_path_str}"}} {metrics["folder_exists"]}\n')
                f.write(f'folder_size_bytes{{path="{backup_path_str}"}} {metrics["folder_size_bytes"]}\n')
                f.write(f'folder_file_count_total{{path="{backup_path_str}"}} {metrics["folder_file_count_total"]}\n')
                f.write(f'folder_latest_file_timestamp{{path="{backup_path_str}"}} {metrics["folder_latest_file_timestamp"]}\n')
                f.write(f'folder_latest_file_size_bytes{{path="{backup_path_str}"}} {metrics["folder_latest_file_size_bytes"]}\n')
            
            logger.info(f"Metrics written to {self.output_file}")
            
        except Exception as e:
            logger.error(f"Error writing metrics file: {e}")
    
    def export_metrics(self) -> None:
        """Collect and export metrics"""
        logger.info("Starting metrics collection...")
        metrics = self.collect_metrics()
        self.write_prometheus_metrics(metrics)
        logger.info("Metrics export completed")
    
    def run_once(self) -> None:
        """Run metrics export once"""
        self.export_metrics()
    
    def run_scheduled(self, interval_minutes: int = 5) -> None:
        """
        Run metrics export on schedule
        
        Args:
            interval_minutes: Interval between exports in minutes
        """
        logger.info(f"Starting scheduled export every {interval_minutes} minutes")
        
        # Schedule the job
        schedule.every(interval_minutes).minutes.do(self.export_metrics)
        
        # Run once immediately
        self.export_metrics()
        
        # Keep running
        while True:
            schedule.run_pending()
            time.sleep(30)  # Check every 30 seconds


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Backup Folder Exporter for Prometheus')
    parser.add_argument(
        '--backup-path', 
        required=True,
        help='Path to backup folder to monitor'
    )
    parser.add_argument(
        '--output-file',
        default='/tmp/backup_metrics.prom',
        help='Output file for Prometheus metrics (default: /tmp/backup_metrics.prom)'
    )
    parser.add_argument(
        '--interval',
        type=int,
        default=5,
        help='Export interval in minutes (default: 5)'
    )
    parser.add_argument(
        '--once',
        action='store_true',
        help='Run once and exit (default: run continuously)'
    )
    
    args = parser.parse_args()
    
    # Create exporter
    exporter = BackupExporter(args.backup_path, args.output_file)
    
    if args.once:
        exporter.run_once()
    else:
        try:
            exporter.run_scheduled(args.interval)
        except KeyboardInterrupt:
            logger.info("Exporter stopped by user")
        except Exception as e:
            logger.error(f"Exporter error: {e}")
            raise


if __name__ == '__main__':
    main()
