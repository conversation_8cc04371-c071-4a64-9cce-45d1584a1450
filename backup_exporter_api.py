#!/usr/bin/env python3
"""
Backup Folder Exporter for Prometheus
Monitors backup folders and exports metrics via HTTP API
"""

import os
import argparse
import logging
from pathlib import Path
from typing import Dict, Any
from datetime import datetime
from flask import Flask, Response, jsonify
import threading
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BackupExporter:
    """Backup folder metrics exporter for Prometheus via HTTP API"""
    
    def __init__(self, backup_path: str, port: int = 8080, update_interval: int = 60):
        """
        Initialize the backup exporter
        
        Args:
            backup_path: Path to the backup folder to monitor
            port: HTTP server port
            update_interval: Metrics update interval in seconds
        """
        self.backup_path = Path(backup_path)
        self.port = port
        self.update_interval = update_interval
        self.metrics = {}
        self.last_update = None
        self.running = False
        
        # Initialize Flask app
        self.app = Flask(__name__)
        self.setup_routes()
        
        logger.info(f"Initialized BackupExporter for path: {self.backup_path}")
        logger.info(f"HTTP API will run on port: {self.port}")
        logger.info(f"Metrics update interval: {self.update_interval} seconds")
    
    def setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/metrics')
        def metrics_endpoint():
            """Prometheus metrics endpoint"""
            return Response(self.format_prometheus_metrics(), mimetype='text/plain')
        
        @self.app.route('/metrics/json')
        def metrics_json_endpoint():
            """JSON metrics endpoint"""
            return jsonify({
                'metrics': self.metrics,
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'backup_path': str(self.backup_path)
            })
        
        @self.app.route('/health')
        def health_endpoint():
            """Health check endpoint"""
            return jsonify({
                'status': 'healthy',
                'backup_path_exists': self.backup_path.exists(),
                'last_update': self.last_update.isoformat() if self.last_update else None,
                'metrics_count': len(self.metrics)
            })
        
        @self.app.route('/')
        def index():
            """Root endpoint with API documentation"""
            return jsonify({
                'service': 'Backup Folder Exporter',
                'version': '1.0',
                'endpoints': {
                    '/metrics': 'Prometheus format metrics',
                    '/metrics/json': 'JSON format metrics',
                    '/health': 'Health check',
                    '/': 'This documentation'
                },
                'backup_path': str(self.backup_path),
                'update_interval': self.update_interval
            })
    
    def collect_metrics(self) -> Dict[str, Any]:
        """
        Collect metrics from the backup folder
        
        Returns:
            Dictionary containing all metrics
        """
        metrics = {
            'folder_exists': 0,
            'folder_size_bytes': 0,
            'folder_file_count_total': 0,
            'folder_latest_file_timestamp': 0,
            'folder_latest_file_size_bytes': 0
        }
        
        try:
            # Check if folder exists
            if not self.backup_path.exists():
                logger.warning(f"Backup path does not exist: {self.backup_path}")
                self.metrics = metrics
                self.last_update = datetime.now()
                return metrics
            
            if not self.backup_path.is_dir():
                logger.warning(f"Backup path is not a directory: {self.backup_path}")
                self.metrics = metrics
                self.last_update = datetime.now()
                return metrics
            
            metrics['folder_exists'] = 1
            
            # Calculate folder size and file count
            total_size = 0
            file_count = 0
            latest_file_time = 0
            latest_file_size = 0
            
            for file_path in self.backup_path.rglob('*'):
                if file_path.is_file():
                    try:
                        file_stat = file_path.stat()
                        file_size = file_stat.st_size
                        file_mtime = file_stat.st_mtime
                        
                        total_size += file_size
                        file_count += 1
                        
                        # Track latest file
                        if file_mtime > latest_file_time:
                            latest_file_time = file_mtime
                            latest_file_size = file_size
                            
                    except (OSError, IOError) as e:
                        logger.warning(f"Error accessing file {file_path}: {e}")
                        continue
            
            metrics['folder_size_bytes'] = total_size
            metrics['folder_file_count_total'] = file_count
            metrics['folder_latest_file_timestamp'] = int(latest_file_time)
            metrics['folder_latest_file_size_bytes'] = latest_file_size
            
            logger.debug(f"Collected metrics: {file_count} files, {total_size} bytes total")
            
        except Exception as e:
            logger.error(f"Error collecting metrics: {e}")
        
        self.metrics = metrics
        self.last_update = datetime.now()
        return metrics
    
    def format_prometheus_metrics(self) -> str:
        """
        Format metrics in Prometheus format
        
        Returns:
            String containing Prometheus formatted metrics
        """
        if not self.metrics:
            self.collect_metrics()
        
        lines = []
        lines.append(f"# Backup folder metrics generated at {datetime.now().isoformat()}")
        lines.append(f"# Monitoring path: {self.backup_path}")
        lines.append("")
        
        backup_path_str = str(self.backup_path)
        lines.append(f'folder_exists{{path="{backup_path_str}"}} {self.metrics.get("folder_exists", 0)}')
        lines.append(f'folder_size_bytes{{path="{backup_path_str}"}} {self.metrics.get("folder_size_bytes", 0)}')
        lines.append(f'folder_file_count_total{{path="{backup_path_str}"}} {self.metrics.get("folder_file_count_total", 0)}')
        lines.append(f'folder_latest_file_timestamp{{path="{backup_path_str}"}} {self.metrics.get("folder_latest_file_timestamp", 0)}')
        lines.append(f'folder_latest_file_size_bytes{{path="{backup_path_str}"}} {self.metrics.get("folder_latest_file_size_bytes", 0)}')
        
        return '\n'.join(lines)
    
    def update_metrics_loop(self):
        """Background thread to update metrics periodically"""
        while self.running:
            try:
                self.collect_metrics()
                logger.info("Metrics updated successfully")
            except Exception as e:
                logger.error(f"Error updating metrics: {e}")
            
            time.sleep(self.update_interval)
    
    def start_server(self, host: str = '0.0.0.0'):
        """
        Start the HTTP server
        
        Args:
            host: Host to bind to
        """
        # Start background metrics update thread
        self.running = True
        metrics_thread = threading.Thread(target=self.update_metrics_loop, daemon=True)
        metrics_thread.start()
        
        # Collect initial metrics
        self.collect_metrics()
        
        logger.info(f"Starting HTTP server on {host}:{self.port}")
        try:
            self.app.run(host=host, port=self.port, debug=False)
        except KeyboardInterrupt:
            logger.info("Server stopped by user")
        finally:
            self.running = False
    
    def stop_server(self):
        """Stop the server"""
        self.running = False


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Backup Folder Exporter HTTP API for Prometheus')
    parser.add_argument(
        '--backup-path', 
        required=True,
        help='Path to backup folder to monitor'
    )
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='HTTP server port (default: 8080)'
    )
    parser.add_argument(
        '--host',
        default='0.0.0.0',
        help='Host to bind to (default: 0.0.0.0)'
    )
    parser.add_argument(
        '--update-interval',
        type=int,
        default=60,
        help='Metrics update interval in seconds (default: 60)'
    )
    
    args = parser.parse_args()
    
    # Create and start exporter
    exporter = BackupExporter(args.backup_path, args.port, args.update_interval)
    
    try:
        exporter.start_server(args.host)
    except Exception as e:
        logger.error(f"Server error: {e}")
        raise


if __name__ == '__main__':
    main()
