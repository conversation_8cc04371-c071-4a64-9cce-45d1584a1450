version: '3.8'

services:
  backup-exporter:
    build: .
    container_name: backup-exporter
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      # Mount your backup folder (change /path/to/your/backup to actual path)
      - /path/to/your/backup:/backup:ro
    environment:
      # Override default settings via environment variables
      - BACKUP_PATH=/backup
      - PORT=8080
      - UPDATE_INTERVAL=60
    command: >
      python backup_exporter_api.py
      --backup-path /backup
      --port 8080
      --host 0.0.0.0
      --update-interval 60
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Prometheus for scraping metrics
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml:ro
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--web.enable-lifecycle'
    depends_on:
      - backup-exporter
