version: '3.8'

services:
  backup-exporter:
    build: .
    container_name: backup-exporter
    restart: unless-stopped
    volumes:
      # Mount your backup folder (change /path/to/your/backup to actual path)
      - /path/to/your/backup:/backup:ro
      # Mount metrics output directory for Node Exporter textfile collector
      - ./metrics:/tmp:rw
    environment:
      # Override default settings via environment variables
      - BACKUP_PATH=/backup
      - OUTPUT_FILE=/tmp/backup_metrics.prom
      - INTERVAL_MINUTES=5
    command: >
      python backup_exporter.py 
      --backup-path /backup 
      --output-file /tmp/backup_metrics.prom 
      --interval 5
    healthcheck:
      test: ["CMD", "test", "-f", "/tmp/backup_metrics.prom"]
      interval: 60s
      timeout: 10s
      retries: 3
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # Optional: Node Exporter for collecting metrics
  node-exporter:
    image: prom/node-exporter:latest
    container_name: node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - ./metrics:/host/textfile:ro
    command:
      - '--path.rootfs=/host'
      - '--collector.textfile.directory=/host/textfile'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    depends_on:
      - backup-exporter

volumes:
  metrics:
    driver: local
