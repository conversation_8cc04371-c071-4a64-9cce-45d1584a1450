#!/bin/bash

# Example usage script for backup-exporter HTTP API

echo "=== Backup Exporter HTTP API Example Usage ==="

# Create example backup directory for testing
echo "Creating example backup directory..."
mkdir -p ./example_backup
echo "This is a test backup file" > ./example_backup/backup1.txt
echo "Another backup file" > ./example_backup/backup2.txt
sleep 1
echo "Latest backup file" > ./example_backup/backup3.txt

echo "Example backup directory created with test files."

# Example 1: Run HTTP API with Python script
echo ""
echo "=== Example 1: Run HTTP API with Python script ==="
echo "Starting HTTP API server in background..."
python backup_exporter_api.py --backup-path ./example_backup --port 8080 &
API_PID=$!

# Wait for server to start
echo "Waiting for server to start..."
sleep 3

echo ""
echo "Testing API endpoints:"

echo "1. Root endpoint:"
curl -s http://localhost:8080/ | python -m json.tool

echo ""
echo "2. Health check:"
curl -s http://localhost:8080/health | python -m json.tool

echo ""
echo "3. Metrics (JSON format):"
curl -s http://localhost:8080/metrics/json | python -m json.tool

echo ""
echo "4. Metrics (Prometheus format):"
curl -s http://localhost:8080/metrics

# Stop the API server
echo ""
echo "Stopping API server..."
kill $API_PID 2>/dev/null
wait $API_PID 2>/dev/null

# Example 2: Build and run with Docker
echo ""
echo "=== Example 2: Build and run with Docker ==="
echo "Building Docker image..."
docker build -t backup-exporter .

echo ""
echo "Running Docker container in background..."
docker run -d \
  --name backup-exporter-test \
  -p 8081:8080 \
  -v $(pwd)/example_backup:/backup:ro \
  backup-exporter

# Wait for container to start
echo "Waiting for container to start..."
sleep 5

echo ""
echo "Testing Docker API:"
curl -s http://localhost:8081/health | python -m json.tool
curl -s http://localhost:8081/metrics/json | python -m json.tool

echo ""
echo "Stopping and removing Docker container..."
docker stop backup-exporter-test
docker rm backup-exporter-test

# Example 3: Docker Compose
echo ""
echo "=== Example 3: Docker Compose ==="
echo "To run with Docker Compose:"
echo "1. Edit docker-compose.yml and change the backup path:"
echo "   - $(pwd)/example_backup:/backup:ro"
echo ""
echo "2. Run: docker-compose up -d"
echo "3. Test API: curl http://localhost:8080/health"
echo "4. View Prometheus: http://localhost:9090"
echo "5. Check logs: docker-compose logs -f backup-exporter"
echo "6. Stop: docker-compose down"

# Cleanup
echo ""
echo "=== Cleanup ==="
echo "Cleaning up example files..."
rm -rf ./example_backup

echo "Example usage completed!"
