#!/bin/bash

# Example usage script for backup-exporter

echo "=== Backup Exporter Example Usage ==="

# Create example backup directory for testing
echo "Creating example backup directory..."
mkdir -p ./example_backup
echo "This is a test backup file" > ./example_backup/backup1.txt
echo "Another backup file" > ./example_backup/backup2.txt
sleep 1
echo "Latest backup file" > ./example_backup/backup3.txt

echo "Example backup directory created with test files."

# Example 1: Run once with Python script
echo ""
echo "=== Example 1: Run once with Python script ==="
echo "Command: python backup_exporter.py --backup-path ./example_backup --output-file ./example_metrics.prom --once"
python backup_exporter.py --backup-path ./example_backup --output-file ./example_metrics.prom --once

echo ""
echo "Generated metrics:"
cat ./example_metrics.prom

# Example 2: Build and run with Docker
echo ""
echo "=== Example 2: Build and run with Docker ==="
echo "Building Docker image..."
docker build -t backup-exporter .

echo ""
echo "Running Docker container..."
docker run --rm \
  -v $(pwd)/example_backup:/backup:ro \
  -v $(pwd)/metrics:/tmp:rw \
  backup-exporter \
  python backup_exporter.py --backup-path /backup --output-file /tmp/backup_metrics.prom --once

echo ""
echo "Docker generated metrics:"
cat ./metrics/backup_metrics.prom 2>/dev/null || echo "Metrics file not found. Check Docker setup."

# Example 3: Docker Compose
echo ""
echo "=== Example 3: Docker Compose ==="
echo "To run with Docker Compose:"
echo "1. Edit docker-compose.yml and change the backup path:"
echo "   - $(pwd)/example_backup:/backup:ro"
echo ""
echo "2. Run: docker-compose up -d"
echo "3. Check logs: docker-compose logs -f backup-exporter"
echo "4. View metrics: cat ./metrics/backup_metrics.prom"
echo "5. Stop: docker-compose down"

# Cleanup
echo ""
echo "=== Cleanup ==="
echo "Cleaning up example files..."
rm -rf ./example_backup
rm -f ./example_metrics.prom
rm -rf ./metrics

echo "Example usage completed!"
